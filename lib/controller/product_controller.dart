import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../services/api_service.dart';
import '../config/app_config.dart';

class ProductController extends GetxController {
  RxList<ProductModel> listProduct = <ProductModel>[].obs;
  RxList<CategoryModel> categories = <CategoryModel>[].obs;
  RxBool loading = true.obs;
  RxBool loadingCategories = true.obs;
  RxInt selectedCategoryId = RxInt(-1); // -1 means all products
  RxString errorMessage = RxString('');

  final ApiService apiService = ApiService();

  @override
  void onInit() {
    super.onInit();
    // Delay initialization to avoid build-time state changes
    Future.microtask(() {
      getCategories();
      getProduct();
    });
  }

  // Fetch all products
  Future<void> getProduct() async {
    try {
      // Set loading state immediately to show loading indicator
      loading(true);
      errorMessage.value = '';

      // Clear current products to ensure UI updates
      listProduct.clear();

      // Fetch all products
      var products = await apiService.getProducts();

      // Update the product list
      listProduct.value = products;
    } catch (e) {
      debugPrint('Error fetching products: $e');
      if (e.toString().contains('401')) {
        errorMessage.value = 'Authentication error: Your API requires authentication. Please check your API configuration.';
      } else {
        errorMessage.value = 'Failed to load products. Please make sure your API is running at ${AppConfig.apiBaseUrl}';
      }
      listProduct.value = [];
    } finally {
      // Ensure loading state is updated
      loading(false);

      // Force UI update by triggering a refresh
      update();
    }
  }

  // Fetch products by category
  Future<void> getProductsByCategory(int categoryId) async {
    try {
      // Set loading state immediately to show loading indicator
      loading(true);
      errorMessage.value = '';
      selectedCategoryId.value = categoryId;

      // Clear current products to ensure UI updates
      listProduct.clear();

      // Fetch products based on category
      List<ProductModel> products;
      if (categoryId == -1) {
        // Show all products
        products = await apiService.getProducts();
      } else {
        // Show filtered products
        products = await apiService.getProductsByCategory(categoryId);
      }

      // Update the product list
      listProduct.value = products;

    } catch (e) {
      print('Error fetching products by category: $e');
      if (e.toString().contains('401')) {
        errorMessage.value = 'Authentication error: Your API requires authentication. Please check your API configuration.';
      } else {
        errorMessage.value = 'Failed to load products for this category. Please make sure your API is running at ${AppConfig.apiBaseUrl}';
      }
      listProduct.value = [];
    } finally {
      // Ensure loading state is updated
      loading(false);

      // Force UI update by triggering a refresh
      update();
    }
  }

  // Fetch all categories
  Future<void> getCategories() async {
    try {
      // Set loading state immediately
      loadingCategories(true);
      errorMessage.value = '';

      // Clear current categories
      categories.clear();

      // Fetch categories
      var fetchedCategories = await apiService.getCategories();

      // Update categories list
      categories.value = fetchedCategories;
    } catch (e) {
      // Log error but use a more production-friendly approach
      debugPrint('Error fetching categories: $e');
      if (e.toString().contains('401')) {
        errorMessage.value = 'Authentication error: Your API requires authentication. Please check your API configuration.';
      } else {
        errorMessage.value = 'Failed to load categories. Please make sure your API is running at ${AppConfig.apiBaseUrl}';
      }
      categories.value = [];
    } finally {
      // Update loading state
      loadingCategories(false);

      // Force UI update
      update();
    }
  }
}
