// lib/widgets/appbar_widget.dart
import 'package:flutter/material.dart';
import 'package:my_first_app/models/cart_model.dart';
import 'package:provider/provider.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback? onMenuPressed;
  final VoidCallback? onCartPressed;

  const CustomAppBar({
    super.key, 
    this.onMenuPressed,
    this.onCartPressed,
  });

  @override
  Size get preferredSize => const Size.fromHeight(70.0);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8.0),
      child: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            GestureDetector(
              onTap: onMenuPressed, // Calls the function when tapped
              child: const CircleAvatar(
                radius: 24,
                backgroundImage: AssetImage('assets/images/Profile.jpg'),
              ),
            ),
            const Sized<PERSON><PERSON>(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Hi",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(
                  "Luffy",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: Icon(Icons.shopping_cart_outlined, color: Colors.grey.shade600, size: 28),
                onPressed: onCartPressed ?? () {
                  // Use the provided callback or fallback to a default behavior
                  if (onCartPressed != null) {
                    onCartPressed!();
                  } else {
                    // Handle the cart action directly if no callback is provided
                    Navigator.pushNamed(context, '/cart');
                  }
                },
              ),
              // Badge showing cart item count
              Positioned(
                top: 8,
                right: 8,
                child: Consumer<CartModel>(
                  builder: (context, cart, child) {
                    return cart.itemCount > 0
                        ? Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.orange,
                              shape: BoxShape.circle,
                            ),
                            child: Text(
                              '${cart.itemCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                        : const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
