import 'package:flutter/material.dart';
import 'package:my_first_app/pages/ContactPage.dart';
import 'package:my_first_app/pages/HomePage.dart';
import 'package:my_first_app/pages/AllProductsPage.dart';
import 'package:my_first_app/screens/cart_screen.dart';
import '../widgets/appbar_widget.dart';

class RomlousApp extends StatefulWidget {
  const RomlousApp({super.key});

  @override
  State<RomlousApp> createState() => _RomlousAppState();
}

class _RomlousAppState extends State<RomlousApp> {
  int _selectedIndex = 0;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  late final List<Widget> _widgetOptions;

  @override
  void initState() {
    super.initState();
    _widgetOptions = <Widget>[
      ExploreScreen(),
      AllProductsPage(),
      Contactpage(),
    ];
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
      _navigatorKey.currentState?.popUntil((route) => route.isFirst);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: CustomAppBar(
        onCartPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const CartScreen(),
            ),
          );
        },
      ),
      body: PopScope(
        child: Navigator(
          key: _navigatorKey,
          onGenerateRoute: (settings) {
            // Handle different routes
            if (settings.name == '/cart') {
              return MaterialPageRoute(
                builder: (context) => const CartScreen(),
              );
            }

            // Default route
            return MaterialPageRoute(
              builder: (context) => _widgetOptions.elementAt(_selectedIndex),
            );
          },
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        color: Colors.white,
        shape: CircularNotchedRectangle(),
        notchMargin: 8.0,
        child: Container(
          height: 80, // Increased height to accommodate CircleAvatar
          padding: EdgeInsets.symmetric(horizontal: 10.0),
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment:
                CrossAxisAlignment.center, // Align items properly
            children: <Widget>[
              _buildNavItem(0, Icons.home, "Home"),
              _buildNavItem(1, Icons.shop, "All Products"),
              _buildNavItem(2, Icons.person_outline, "Contact"),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    bool isSelected = _selectedIndex == index;
    return Expanded(
      child: MaterialButton(
        onPressed: () => _onItemTapped(index),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 28,
              color: isSelected ? Colors.black : Colors.grey.shade400,
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? Colors.black : Colors.grey.shade400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
