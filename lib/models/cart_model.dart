import 'package:flutter/foundation.dart';
import 'package:my_first_app/models/product_model.dart';

class CartItem {
  final ProductModel product;
  int quantity;

  CartItem({
    required this.product,
    this.quantity = 1,
  });

  double get total => product.price * quantity;
}

class CartModel extends ChangeNotifier {
  // Private internal state
  final List<CartItem> _items = [];

  // Getters
  List<CartItem> get items => List.unmodifiable(_items);
  
  int get itemCount => _items.fold(0, (total, item) => total + item.quantity);
  
  double get totalPrice => _items.fold(
        0,
        (total, item) => total + (item.product.price * item.quantity),
      );

  // Check if product is in cart
  bool isInCart(ProductModel product) {
    return _items.any((item) => item.product.id == product.id);
  }

  // Get cart item by product id
  CartItem? getCartItem(int productId) {
    try {
      return _items.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  // Add item to cart
  void addItem(ProductModel product) {
    // Check if product already exists in cart
    final existingItem = getCartItem(product.id);
    
    if (existingItem != null) {
      // If exists, increase quantity
      existingItem.quantity++;
    } else {
      // If not, add new item
      _items.add(CartItem(product: product));
    }
    
    // Notify listeners that the cart has changed
    notifyListeners();
  }

  // Remove item from cart
  void removeItem(int productId) {
    _items.removeWhere((item) => item.product.id == productId);
    notifyListeners();
  }

  // Increase quantity of an item
  void increaseQuantity(int productId) {
    final cartItem = getCartItem(productId);
    if (cartItem != null) {
      cartItem.quantity++;
      notifyListeners();
    }
  }

  // Decrease quantity of an item
  void decreaseQuantity(int productId) {
    final cartItem = getCartItem(productId);
    if (cartItem != null) {
      if (cartItem.quantity > 1) {
        cartItem.quantity--;
      } else {
        removeItem(productId);
      }
      notifyListeners();
    }
  }

  // Clear the cart
  void clear() {
    _items.clear();
    notifyListeners();
  }
} 