class PayPalPaymentRequest {
  final String total;
  final String currency;
  final String description;
  final List<PayPalItem> items;

  PayPalPaymentRequest({
    required this.total,
    required this.currency,
    required this.description,
    required this.items,
  });

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'currency': currency,
      'description': description,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

class PayPalItem {
  final String name;
  final String quantity;
  final String price;
  final String currency;

  PayPalItem({
    required this.name,
    required this.quantity,
    required this.price,
    required this.currency,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'quantity': quantity,
      'price': price,
      'currency': currency,
    };
  }
}

class PayPalPaymentResult {
  final bool success;
  final String? paymentId;
  final String? payerId;
  final String? error;

  PayPalPaymentResult({
    required this.success,
    this.paymentId,
    this.payerId,
    this.error,
  });
}
