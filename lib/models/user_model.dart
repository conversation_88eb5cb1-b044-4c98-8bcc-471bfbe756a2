class UserModel {
  final int id;
  final String name;
  final String email;
  final String profileImageUrl;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.profileImageUrl,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    final int userId = json['id'] ?? 1; 
    final String name = json['name'] ?? 'Guest User';
    final baseApiUrl = 'http://127.0.0.1:8000/api';
    
    String profileImage = json['profile_image'] ?? '';
    if (profileImage.isEmpty) {
      profileImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(name)}&background=FFA500&color=fff';
      
      } else if (!profileImage.startsWith('http')) {
      profileImage = '$baseApiUrl/users/$userId/profile/image';
    }

    return UserModel(
      id: userId,
      name: name,
      email: json['email'] ?? '<EMAIL>',
      profileImageUrl: profileImage,
    );
  }

  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? profileImageUrl,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }
} 