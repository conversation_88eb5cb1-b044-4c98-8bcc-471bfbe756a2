import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/ProductCard.dart';
import '../controller/product_controller.dart';

class ProductPage extends StatelessWidget {
  final ProductController productController = Get.put(ProductController());

  ProductPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        backgroundColor: Colors.white,
        body: CustomScrollView(
          slivers: [
            // Fixed Search Bar (SliverAppBar)
            SliverAppBar(
              backgroundColor: Colors.white,
              floating: true, // Ensures the search bar is always visible
              pinned: true, // Keeps the search bar fixed at the top
              elevation: 0,
              flexibleSpace: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: _buildSearchBar(),
              ),
            ),

            // Category Selector
            SliverToBoxAdapter(
              child: _buildCategorySelector(),
            ),

            // Scrollable Product Cards with real data
            SliverPadding(
              padding: const EdgeInsets.only(top: 10, left: 14, right: 14),
              sliver: Obx(() {
                if (productController.loading.value) {
                  return const SliverFillRemaining(
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                // Show error message if there is one
                if (productController.errorMessage.value.isNotEmpty) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 60,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            productController.errorMessage.value,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.red,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              if (productController.selectedCategoryId.value == -1) {
                                productController.getProduct();
                              } else {
                                productController.getProductsByCategory(
                                  productController.selectedCategoryId.value
                                );
                              }
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (productController.listProduct.isEmpty) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Text(
                        'No products found in this category',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  );
                }

                return SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 8.0,
                    mainAxisSpacing: 8.0,
                    childAspectRatio: 0.58,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final product = productController.listProduct[index];
                      return ProductCard(
                        name: product.name,
                        price: '\$${product.price}',
                        imageUrl: product.imageUrl,
                        product: product, // Pass the product model to enable details view
                        onAddToCart: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('${product.name} added to cart!'),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        },
                      );
                    },
                    childCount: productController.listProduct.length,
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  // Category selector widget
  Widget _buildCategorySelector() {
    return Obx(() {
      if (productController.loadingCategories.value) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: CircularProgressIndicator(),
          ),
        );
      }

      // Show error message for categories if there is one
      if (productController.errorMessage.value.isNotEmpty && productController.categories.isEmpty) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
          child: Center(
            child: Text(
              'Failed to load categories. Showing all products.',
              style: TextStyle(color: Colors.orange[800]),
            ),
          ),
        );
      }

      if (productController.categories.isEmpty) {
        return const SizedBox.shrink(); // Don't show category selector if no categories
      }

      return Container(
        height: 50,
        margin: const EdgeInsets.only(top: 8.0),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          itemCount: productController.categories.length + 1, // +1 for "All" option
          itemBuilder: (context, index) {
            // First item is "All Products"
            if (index == 0) {
              return _buildCategoryChip(
                "All Products",
                -1,
                productController.selectedCategoryId.value == -1
              );
            }

            // Adjust index for actual categories
            final category = productController.categories[index - 1];
            return _buildCategoryChip(
              category.name,
              category.id,
              productController.selectedCategoryId.value == category.id
            );
          },
        ),
      );
    });
  }

  // Individual category chip
  Widget _buildCategoryChip(String name, int categoryId, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: GestureDetector(
        onTap: () {
          productController.getProductsByCategory(categoryId);
        },
        child: Chip(
          label: Text(
            name,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.black87,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          backgroundColor: isSelected ? Colors.blue : Colors.grey.shade200,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
      ),
    );
  }

  // Modern and clean search bar widget
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4), // Shadow position
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.search, color: Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'Search for products',
                hintStyle: TextStyle(color: Colors.grey),
                border: InputBorder.none,
              ),
              onChanged: (value) {
                // Add logic for search functionality here
                // This will be implemented in a future update
              },
            ),
          ),
        ],
      ),
    );
  }
}
