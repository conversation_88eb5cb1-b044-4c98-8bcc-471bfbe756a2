import 'package:flutter/material.dart';
import 'dart:async';

import 'package:my_first_app/authentications/login.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});
  @override
  _IntroScreenState createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  @override
  void initState() {
    super.initState();
    Timer(const Duration(seconds: 5), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LoginApp(), // Transition to ProductListScreen
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset("assets/images/intro-screen.png", height: 250),
            const SizedBox(height: 30),
            const Text(
              "Have a nice day !",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color.fromARGB(255, 159, 159, 159),
              ),
            ),
            const SizedBox(height: 10),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                "Welcome to Romlous",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 25,
                  color: Color.fromARGB(255, 159, 159, 159),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

//Correct with 59
