import 'package:flutter/material.dart';
import 'package:flutter_paypal_payment/flutter_paypal_payment.dart';
import '../models/paypal_models.dart';
import '../models/cart_model.dart';

class PayPalService {
  // PayPal configuration - Replace with your actual PayPal credentials
  static const String _clientId = 'YOUR_PAYPAL_CLIENT_ID'; // Replace with your PayPal Client ID
  static const String _clientSecret = 'YOUR_PAYPAL_CLIENT_SECRET'; // Replace with your PayPal Client Secret
  static const bool _sandboxMode = true; // Set to false for production

  // Getter to check if PayPal is configured
  static String get clientId => _clientId;

  static Future<PayPalPaymentResult> processPayment({
    required BuildContext context,
    required CartModel cart,
    required double shippingCost,
  }) async {
    try {
      // Calculate total amount
      final double totalAmount = cart.totalPrice + shippingCost;
      
      // Create PayPal items from cart
      final List<PayPalItem> paypalItems = cart.items.map((cartItem) {
        return PayPalItem(
          name: cartItem.product.name,
          quantity: cartItem.quantity.toString(),
          price: cartItem.product.price.toStringAsFixed(2),
          currency: 'USD',
        );
      }).toList();

      // Add shipping as an item if there's a shipping cost
      if (shippingCost > 0) {
        paypalItems.add(PayPalItem(
          name: 'Shipping',
          quantity: '1',
          price: shippingCost.toStringAsFixed(2),
          currency: 'USD',
        ));
      }

      // Navigate to PayPal payment
      Navigator.of(context).push(MaterialPageRoute(
        builder: (BuildContext context) => PaypalCheckoutView(
          sandboxMode: _sandboxMode,
          clientId: _clientId,
          secretKey: _clientSecret,
          transactions: [
            {
              "amount": {
                "total": totalAmount.toStringAsFixed(2),
                "currency": "USD",
                "details": {
                  "subtotal": cart.totalPrice.toStringAsFixed(2),
                  "shipping": shippingCost.toStringAsFixed(2),
                  "shipping_discount": "0.00"
                }
              },
              "description": "Payment for your order from Explore App",
              "payment_options": {
                "allowed_payment_method": "INSTANT_FUNDING_SOURCE"
              },
              "item_list": {
                "items": paypalItems.map((item) => {
                  "name": item.name,
                  "quantity": item.quantity,
                  "price": item.price,
                  "currency": item.currency
                }).toList(),
              }
            }
          ],
          note: "Contact us for any questions on your order.",
          onSuccess: (Map params) async {
            print("PayPal Payment Success: $params");
            Navigator.pop(context);
            return PayPalPaymentResult(
              success: true,
              paymentId: params['paymentId'],
              payerId: params['payerID'],
            );
          },
          onError: (error) {
            print("PayPal Payment Error: $error");
            Navigator.pop(context);
            return PayPalPaymentResult(
              success: false,
              error: error.toString(),
            );
          },
          onCancel: () {
            print('PayPal Payment Cancelled');
            Navigator.pop(context);
            return PayPalPaymentResult(
              success: false,
              error: 'Payment cancelled by user',
            );
          },
        ),
      ));

      // Return a default result - the actual result will be handled in the callbacks
      return PayPalPaymentResult(success: false, error: 'Payment in progress');

    } catch (e) {
      print('PayPal Service Error: $e');
      return PayPalPaymentResult(
        success: false,
        error: 'Failed to initialize PayPal payment: $e',
      );
    }
  }

  static void showPayPalSetupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('PayPal Setup Required'),
          content: const Text(
            'To use PayPal payments, you need to:\n\n'
            '1. Create a PayPal Developer account\n'
            '2. Create a PayPal app to get Client ID and Secret\n'
            '3. Replace the placeholder credentials in PayPalService\n\n'
            'Visit: https://developer.paypal.com/',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
